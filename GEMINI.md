# Gemini Project Helper

This file helps the <PERSON> assistant understand the project structure, conventions, and common commands.

## Project Overview

StdStation appears to be a C++/Qt desktop application for monitoring and managing data from various devices, possibly in an industrial or laboratory setting. It features a graphical user interface, database interaction, and communication via Modbus.

## Build Instructions

The project uses CMake for building. To build the project, run the following commands from the root directory:

```bash
cmake -B build
cmake --build build
```

## Code Formatting

The project uses `.clang-format` for code style consistency. A Python script is provided to automate formatting.

To format all relevant source files, run:

```bash
python fromat.py code/
```

## Key Directories

- `code/`: Contains the core C++ source code.
  - `common/`: Utility classes and common definitions.
  - `core/`: Main application logic and system management.
  - `devices/`: Logic for interacting with specific hardware devices.
  - `modbus/`: Modbus communication implementation.
  - `sql/`: Database interaction logic.
  - `window/`: UI-related source code, including window management and custom components.
- `ui/`: Qt Designer `.ui` files for the user interface.
- `libs/`: Third-party libraries.
  - `json/`: nlohmann/json for JSON parsing.
  - `modbus/`: libmodbus for Modbus communication.
  - `qtpromise/`: QtPromise for asynchronous operations.
- `resource/`: Application icons, images, and other resources.

## Dependencies

- **Qt**: The core application framework.
- **nlohmann/json**: For handling JSON data.
- **libmodbus**: For Modbus communication.
- **QtPromise**: For asynchronous programming patterns.

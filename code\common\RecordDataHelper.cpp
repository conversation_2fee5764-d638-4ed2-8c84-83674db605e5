#include "RecordDataHelper.h"

#include <QHeaderView>

#include "../common/ConstData.h"
#include "../sql/database.h"
#include "../sql/errorlog.h"
#include "../sql/upload_database.h"

void RecordDataHelper::setupTableHeaders(QTableWidget *tableWidget, RecordType recordType)
{
    if (!tableWidget) return;

    switch (recordType) {
        case RecordType::DeviceData:
            setupDeviceDataHeaders(tableWidget);
            break;
        case RecordType::UploadData:
            setupUploadRecordHeaders(tableWidget);
            break;
        case RecordType::ErrorData:
            setupErrorLogHeaders(tableWidget);
            break;
    }
}

void RecordDataHelper::loadPageData(QTableWidget *tableWidget, RecordType recordType,
                                    int currentPage, int pageSize, const QDate &queryDate)
{
    if (!tableWidget) return;

    // 清空表格内容
    tableWidget->clearContents();
    tableWidget->setRowCount(0);

    // 设置表头
    setupTableHeaders(tableWidget, recordType);

    // 根据记录类型加载数据
    switch (recordType) {
        case RecordType::DeviceData:
            loadDeviceData(tableWidget, currentPage, pageSize, queryDate);
            break;
        case RecordType::UploadData:
            loadUploadRecordData(tableWidget, currentPage, pageSize, queryDate);
            break;
        case RecordType::ErrorData:
            loadErrorLogData(tableWidget, currentPage, pageSize, queryDate);
            break;
    }
}

int RecordDataHelper::getTotalRecords(RecordType recordType, const QDate &queryDate)
{
    switch (recordType) {
        case RecordType::DeviceData:
            return Database::getTotalRecords(queryDate);
        case RecordType::UploadData:
            return UploadDatabase::getTotalUploadRecords(queryDate);
        case RecordType::ErrorData:
            return ErrorLog::getTotalRecords(queryDate);
        default:
            return 0;
    }
}

void RecordDataHelper::setupDeviceDataHeaders(QTableWidget *tableWidget)
{
    QStringList headers;
    headers << "时间";  // 第一个表头是 timestamp

    for (const QString &name : DEVICE_NAMES) {
        if (DEVICE_MAP[name].isShowData) {
            headers << name;
        }
    }

    tableWidget->setColumnCount(headers.size());
    tableWidget->setHorizontalHeaderLabels(headers);
    tableWidget->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);
    tableWidget->horizontalHeader()->setSectionResizeMode(0, QHeaderView::Fixed);  // 固定第一列
}

void RecordDataHelper::loadDeviceData(QTableWidget *tableWidget, int currentPage, int pageSize,
                                      const QDate &queryDate)
{
    // 使用Database类的分页查询方法
    QVector<QVariantMap> data = Database::queryByPage(currentPage, pageSize, queryDate);

    int rowIdx = 0;
    for (const QVariantMap &record : data) {
        tableWidget->insertRow(rowIdx);

        // 时间戳 (第0列)
        QTableWidgetItem *itemTimestamp =
            new QTableWidgetItem(record.value("timestamp").toString());
        itemTimestamp->setTextAlignment(Qt::AlignCenter);
        tableWidget->setItem(rowIdx, 0, itemTimestamp);

        // 模块数据 (从第1列开始)
        int colIdx = 1;
        for (const QString &name : DEVICE_NAMES) {
            if (!DEVICE_MAP[name].isShowData) continue;

            QVariant moduleValue = record.value(name);
            QString displayText;
            if (moduleValue.isNull()) {
                displayText = "- -";
            } else {
                displayText = QString::number(moduleValue.toDouble(), 'f', 3);
            }

            QTableWidgetItem *itemModuleData = new QTableWidgetItem(displayText);
            itemModuleData->setTextAlignment(Qt::AlignCenter);
            tableWidget->setItem(rowIdx, colIdx, itemModuleData);
            colIdx++;
        }

        rowIdx++;
    }
}

void RecordDataHelper::setupUploadRecordHeaders(QTableWidget *tableWidget)
{
    QStringList headers;
    headers << "上传时间"
            << "服务器"
            << "状态"
            << "数据包";

    tableWidget->setColumnCount(headers.size());
    tableWidget->setHorizontalHeaderLabels(headers);
    tableWidget->horizontalHeader()->setSectionResizeMode(QHeaderView::ResizeToContents);
    tableWidget->horizontalHeader()->setSectionResizeMode(0, QHeaderView::Fixed);  // 固定第一列
}

void RecordDataHelper::loadUploadRecordData(QTableWidget *tableWidget, int currentPage,
                                            int pageSize, const QDate &queryDate)
{
    // 使用UploadDatabase类的分页查询方法
    QVector<QVariantMap> data =
        UploadDatabase::queryUploadRecordsByPage(currentPage, pageSize, queryDate);

    int rowIdx = 0;
    for (const QVariantMap &record : data) {
        tableWidget->insertRow(rowIdx);

        int colIdx = 0;

        // 上传时间
        QTableWidgetItem *timeItem = new QTableWidgetItem(record.value("upload_time").toString());
        timeItem->setTextAlignment(Qt::AlignCenter);
        tableWidget->setItem(rowIdx, colIdx++, timeItem);

        // 服务器信息
        QString serverInfo = QString("%1:%2")
                                 .arg(record.value("server_ip").toString())
                                 .arg(record.value("server_port").toInt());
        QTableWidgetItem *serverItem = new QTableWidgetItem(serverInfo);
        serverItem->setTextAlignment(Qt::AlignCenter);
        tableWidget->setItem(rowIdx, colIdx++, serverItem);

        // 状态
        QTableWidgetItem *statusItem = new QTableWidgetItem(record.value("status_text").toString());
        statusItem->setTextAlignment(Qt::AlignCenter);
        tableWidget->setItem(rowIdx, colIdx++, statusItem);

        // 数据包
        QString dataContent = record.value("data_content").toString();
        QTableWidgetItem *dataItem = new QTableWidgetItem(dataContent);
        dataItem->setTextAlignment(Qt::AlignCenter);
        tableWidget->setItem(rowIdx, colIdx++, dataItem);

        rowIdx++;
    }
}

void RecordDataHelper::setupErrorLogHeaders(QTableWidget *tableWidget)
{
    QStringList headers;
    headers << "时间"
            << "错误信息";

    tableWidget->setColumnCount(headers.size());
    tableWidget->setHorizontalHeaderLabels(headers);
    tableWidget->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);
    tableWidget->horizontalHeader()->setSectionResizeMode(0, QHeaderView::Fixed);  // 固定第一列
}

void RecordDataHelper::loadErrorLogData(QTableWidget *tableWidget, int currentPage, int pageSize,
                                        const QDate &queryDate)
{
    QVector<QVariantMap> data = ErrorLog::queryByPage(currentPage, pageSize, queryDate);

    int rowIdx = 0;
    for (const QVariantMap &record : data) {
        tableWidget->insertRow(rowIdx);

        int colIdx = 0;

        // 时间
        QTableWidgetItem *timeItem = new QTableWidgetItem(record.value("timestamp").toString());
        timeItem->setTextAlignment(Qt::AlignCenter);
        tableWidget->setItem(rowIdx, colIdx++, timeItem);

        // 错误信息
        QTableWidgetItem *errorItem =
            new QTableWidgetItem(record.value("error_message").toString());
        errorItem->setTextAlignment(Qt::AlignCenter);
        tableWidget->setItem(rowIdx, colIdx++, errorItem);

        rowIdx++;
    }
}
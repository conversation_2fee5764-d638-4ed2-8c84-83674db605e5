# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build System

This is a Qt5 C++ application built with CMake. Key build commands:

```bash
# Build the project
mkdir build && cd build
cmake ..
make

# For Windows with Visual Studio
cmake -G "Visual Studio 16 2019" ..
cmake --build . --config Release
```

## Code Formatting

The project uses `.clang-format` for code style consistency. A Python script is provided to automate formatting.

To format all relevant source files, run:

```bash
python fromat.py code/
```

## Project Architecture

### Core Architecture
The application follows a layered architecture pattern with clear separation of concerns:

**System Layer (`code/core/`)**
- `System`: Main system coordinator - singleton providing unified interface
- `SystemScheduler`: Handles scheduled tasks and timing operations
- `SystemConfig`: Configuration management for system-wide settings
- `TaskStateMachine`: State machine for complex multi-step operations

**Common Utilities Layer (`code/common/`)**
- `ButtonBeep`: Audio feedback for button interactions
- `ConfigManager`: Configuration file management and parsing
- `ConstData`/`ConstNames`: System constants and naming conventions
- `RecordDataHelper`: Data recording and processing utilities
- `SvgController`: SVG graphics control and manipulation
- `common`: Shared utility functions and definitions

**Device Layer (`code/devices/`)**
- `DeviceManager`: Singleton device registry and lifecycle management
- `IDevice`: Base interface for all device implementations
- `DeviceHelper`: Specific device implementations and communication
- Device-specific helpers (WCS, YCL) for specialized equipment

**Data Layer (`code/sql/`)**
- `database.h/cpp`: Main database operations
- `user_database.h/cpp`: User management and authentication
- `upload_database.h/cpp`: Data upload tracking
- `errorlog.h/cpp`: Error logging and reporting

**Communication Layer (`code/modbus/`)**
- `ModbusManager`: Modbus protocol management
- `ModbusHelper`: Modbus communication utilities

**Update Layer (`code/update/`)**
- `AppUpdater`: Application version checking and update management

**Upload Layer (`code/upload/`)**
- `Upload`: Core upload functionality and coordination
- `UploadConfig`: Upload configuration management
- `UploadHelper`: Upload utility functions and data preparation
- `UploadWorker`: Background worker for upload operations

**UI Layer (`code/window/`)**
- `MainWindow`: Primary application window and navigation
- Page implementations: `Home`, `operate`, `realdata`, `records`, etc.
- Component layer: `CardTemplate`, `notification_manager`, `operationWindow`

### Task Execution Flow
1. **System.execTask()** → delegates to SystemScheduler
2. **SystemScheduler.executeTask()** → uses TaskStateMachine  
3. **TaskStateMachine** → executes composite tasks as sequences of atomic operations
4. **Atomic operations** → interact with DeviceManager to control specific devices

### Key Design Patterns
- **Singleton Pattern**: System, DeviceManager, NotificationManager
- **State Machine Pattern**: TaskStateMachine for complex workflows
- **Observer Pattern**: Qt signals/slots for component communication
- **Strategy Pattern**: Different device helpers implementing IDevice interface

## Code Organization

### Dependencies
- **Qt5**: Core GUI framework (Widgets, Sql, Network, Svg, Concurrent)
- **nlohmann/json**: JSON parsing (in `libs/json/`)
- **libmodbus**: Modbus communication (in `libs/modbus/`)
- **QtPromise**: Promise-based async operations (in `libs/qtpromise/`)

### Database Schema
Uses SQLite database (`database.db`) with tables for:
- Device readings and measurements
- User accounts and permissions  
- Upload records and synchronization
- Error logs and system events

### Configuration
- System settings stored in database
- Main UI resources in `res.qrc`
- Window-specific resources in `code/window/resources.qrc`
- Application icon and styling in `style.qss`
- Resource files in `resource/` directory

## Development Workflow

### Git Workflow
For code review and collaboration, use the following git commands:

```bash
# Standard development workflow
git add .
git commit -m "Your commit message"

# Push for code review (Gerrit-style)
git push origin HEAD:refs/for/master

# Push to specific branch for review
git push origin HEAD:refs/for/branch-name
```

### Testing Data Generation
Use the Python script to generate test data:
```bash
python generate_test_data.py
```
This creates sample device readings in `database.db` for testing.

### Key Entry Points
- `main.cpp`: Application initialization, database setup, logging configuration
- `MainWindow`: UI initialization and page navigation
- `System::instance()`: Access to core system functionality

### Device Integration
New devices should:
1. Implement `IDevice` interface
2. Register with `DeviceManager` 
3. Define atomic operations in `TaskStateMachine` if needed
4. Handle Modbus communication through `ModbusManager`

### Debugging
- Debug builds show console output
- Release builds log to `log/YYYY-MM-DD/StdStation.log`
- Use `showInfo()`, `showWarning()`, `showError()` macros for user notifications

## Common Operations

### Adding New Scheduled Tasks
1. Define composite task in `TaskStateMachine::defineBuiltinTasks()`
2. Add timing logic to `SystemScheduler::calculateNextTime()`
3. Update `SystemInfo` struct if new schedule type needed

### Adding New Device Types
1. Create device helper class inheriting from `IDevice`
2. Register in `DeviceManager` constructor
3. Add device-specific atomic operations to `TaskStateMachine`
4. Update UI components to display device status

### Database Schema Changes
1. Update relevant database class (`Database`, `UserDatabase`, etc.)
2. Add migration logic in `initTable()` methods
3. Update any related UI components showing the data